# Payroll Management System (PMS)

A Django-based Payroll Management System for managing employee payrolls, departments, and administrative tasks.

## Features

- Employee management
- Payroll generation and management
- Department and designation management
- Admin, Staff, and Accountant roles
- Email notifications
- Secure authentication with custom user model

## Prerequisites

- Python 3.12+
- MySQL database
- Git

## Quick Setup

### 1. Clone and Setup Environment

```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd PMS

# Run the automated setup script
python3 setup.py
```

### 2. Manual Setup (Alternative)

```bash
# Create virtual environment
python3 -m venv venv

# Activate virtual environment
# On Linux/Mac:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Configure Environment Variables

Update the `.env` file with your actual configuration:

```env
# Django Configuration
SECRET_KEY=your-generated-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_HOST=localhost
DB_PORT=3306

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Google reCAPTCHA (for production)
GOOGLE_RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key
GOOGLE_RECAPTCHA_SITE_KEY=your-recaptcha-site-key
```

### 4. Database Setup

```bash
# Create database migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser
```

### 5. Run Development Server

```bash
python manage.py runserver
```

Visit `http://127.0.0.1:8000` to access the application.

## Project Structure

```
PMS/
├── PMS/                    # Django project settings
├── main/                   # Main application
├── templates/              # HTML templates
├── static/                 # Static files (CSS, JS, images)
├── venv/                   # Virtual environment
├── .env                    # Environment variables
├── requirements.txt        # Python dependencies
├── manage.py              # Django management script
└── README.md              # This file
```

## User Roles

1. **Admin**: Full system access, user management
2. **Accountant**: Payroll generation and management
3. **Staff**: View personal payroll information

## Development

### Running Tests

```bash
python manage.py test
```

### Collecting Static Files

```bash
python manage.py collectstatic
```

### Creating New Migrations

```bash
python manage.py makemigrations
python manage.py migrate
```

## Production Deployment

1. Set `DEBUG=False` in `.env`
2. Configure proper `ALLOWED_HOSTS`
3. Set up proper database and email configurations
4. Configure reCAPTCHA keys
5. Use a proper web server (nginx + gunicorn)
6. Set up SSL certificates

## Troubleshooting

### Common Issues

1. **Database Connection Error**: Check your database credentials in `.env`
2. **Email Not Working**: Verify email settings or use console backend for development
3. **Static Files Not Loading**: Run `python manage.py collectstatic`

### Support

For issues and questions, please check the documentation or create an issue in the repository.
