{% extends 'main_app/base.html' %}
{% load static %}
{% block page_title %}{{page_title}}{% endblock page_title %}

{% block content %}

<!-- Main content -->
<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-3"></div>
      <div class="col-md-6">
        <!-- Profile Image -->
        <div class="card card-primary card-outline">
          <div class="card-body box-profile">
            <div class="text-center">
              {% if user.profile_pic %}
              <img class="profile-user-img img-fluid img-circle" src="{{ user.profile_pic}}" alt="User profile picture">
              {% else %}
              <img class="profile-user-img img-fluid img-circle" src="{% static 'dist/img/default-avatar.png' %}"
                alt="User profile picture">
              {% endif %}
            </div>

            <h3 class="profile-username text-center">{{ user.first_name }} {{ user.last_name }}</h3>
            <p class="text-muted text-center">Staff</p>
            <ul class="list-group list-group-unbordered mb-3">
              <li class="list-group-item">
                <b>Email</b> <a class="float-right">{{ staff.user.email }}</a>
              </li>
              <li class="list-group-item">
                <b>Father's Name</b> <a class="float-right">{{ staff.user.father_name }}</a>
              </li>
              <li class="list-group-item">
                <b>Gender</b> <a class="float-right">{{ staff.user.gender }}</a>
              </li>
              <li class="list-group-item">
                <b>Date of Joning</b> <a class="float-right">{{ staff.emp_doj }}</a>
              </li>
              <li class="list-group-item">
                <b>Empolye Code</b> <a class="float-right">{{ staff.emp_code }}</a>
              </li>
              <li class="list-group-item">
                <b>UNA Code</b> <a class="float-right">{{ staff.uan }}</a>
              </li>
              <!-- <li class="list-group-item">
                <b>Division</b> <a class="float-right">{{ staff.division }}</a>
              </li> -->
              <li class="list-group-item">
                <b>Department</b> <a class="float-right">{{ staff.department }}</a>
              </li>
              <li class="list-group-item">
                <b>Designations</b> <a class="float-right">{{ staff.designation }}</a>
              </li>
              <!-- <li class="list-group-item">
                <b>Basic Salary</b> <a class="float-right">{{ staff.basic_amt }}</a>
              </li> -->

            </ul>
          </div>
          <!-- /.card-body -->
        </div>
        <!-- /.card -->
      </div>
      <div class="col-md-3"></div>
      <!-- /.col -->
    </div>
    <!-- /.row -->
  </div><!-- /.container-fluid -->
</section>

<!-- /.content -->
{% endblock content %}

{% block custom_js %}
<script>
  $(document).ready(function () {
    var ifNotfied = false;
    $("#id_password").on("change", function () {
      if (!ifNotfied) {
        ifNotfied = true;
        alert("After a successful profile update:\n\nYour session would be terminated\nYou would be required to login again")
      }
    })
  })
</script>
{% endblock custom_js %}