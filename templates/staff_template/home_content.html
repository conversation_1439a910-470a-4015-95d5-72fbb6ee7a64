{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Staff Dashboard{% endblock page_title %}
{% block extra_css %}
<style>
  /* Custom CSS for the dashboard */
  .card-info .card-header {
    background-color: #17a2b8; /* Brighter blue for the header */
    color: white;
  }

  .list-group-item {
    border-bottom: 1px solid #f0f0f0; /* Softer border for list items */
  }
</style>
{% endblock extra_css %}

{% block content %}
<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card card-info">
          <div class="card-header">
            <h3 class="card-title">Welcome, {{ user.first_name }}!</h3>
          </div>
          <div class="card-body">
            {% if recent_payslips %}
              <p>Here are your recent payslips from {{ start_date }} to {{ end_date }}:</p>
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>Month</th>
                    <th>Net Pay</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for payslip in recent_payslips %}
                    <tr>
                      <td>{{ payslip.month|date:'F Y' }}</td>
                      <td>₹{{ payslip.net_pay }}</td>
                      <td>
                        <a href="{% url 'view_payslip_staff' user.staff.id payslip.month|date:'Y-m-d' %}" 
                          class="btn btn-sm btn-info" title="View Payslip">
                          <i class="fas fa-eye"></i>
                        </a> 
                        </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            {% else %}
              <p>No payslips found for the selected period.</p>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock content %}
