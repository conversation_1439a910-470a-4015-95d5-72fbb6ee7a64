{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Manage Fixed Allowances{% endblock page_title %}

{% block content %}

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- Enhanced Fixed Allowances Management Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-percentage mr-2"></i>{{ page_title }}
                        </h3>
                        <div class="card-tools">
                            <div class="btn-group">
                                <a href="{% url 'manage_fixed' %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus mr-1"></i> Add New Allowance
                                </a>
                                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown">
                                    <span class="sr-only">Toggle Dropdown</span>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{% url 'fixed' %}">
                                        <i class="fas fa-list mr-2"></i>All Allowances
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- Enhanced DataTable -->
                        <div class="table-responsive">
                            <table id="example1" class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th style="width: 50px;">#</th>
                                        <th>Division</th>
                                        <th>Month</th>
                                        <th>DA (%)</th>
                                        <th>HRA (%)</th>
                                        <th style="width: 150px;" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for allowance in fixed_allowances %}
                                    <tr>
                                        <td>
                                            <span class="badge badge-secondary">{{ forloop.counter }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="mr-3">
                                                    <div class="bg-info rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                        <i class="fas fa-sitemap text-white"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong>{{ allowance.division }}</strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-building mr-1"></i>Division
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">
                                                <i class="fas fa-calendar mr-1"></i>{{ allowance.month|date:"F Y" }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge badge-success badge-lg">
                                                <i class="fas fa-percentage mr-1"></i>{{ allowance.da }}%
                                            </span>
                                            <br>
                                            <small class="text-muted">Dearness Allowance</small>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge badge-warning badge-lg">
                                                <i class="fas fa-home mr-1"></i>{{ allowance.hra }}%
                                            </span>
                                            <br>
                                            <small class="text-muted">House Rent Allowance</small>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'manage_fixed' allowance.id %}" class="btn btn-info btn-sm" title="Edit Allowance">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'delete_fixed' allowance.id %}" class="btn btn-danger btn-sm"
                                                   title="Delete Allowance" data-confirm="Are you sure you want to delete this fixed allowance?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-percentage fa-3x mb-3"></i>
                                                <h5>No Fixed Allowances Found</h5>
                                                <p>Start by adding your first fixed allowance percentage.</p>
                                                <a href="{% url 'manage_fixed' %}" class="btn btn-primary">
                                                    <i class="fas fa-plus mr-2"></i>Add Fixed Allowance
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}
