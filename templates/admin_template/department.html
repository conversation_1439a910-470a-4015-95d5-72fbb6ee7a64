{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}

{% block content %}

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- Enhanced Department Management Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-building mr-2"></i>{{ page_title }}
                        </h3>
                        <div class="card-tools">
                            <div class="btn-group">
                                <a href="{% url 'manage_department' %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus mr-1"></i> Add New Department
                                </a>
                                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown">
                                    <span class="sr-only">Toggle Dropdown</span>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{% url 'department' %}">
                                        <i class="fas fa-list mr-2"></i>All Departments
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- Enhanced DataTable -->
                        <div class="table-responsive">
                            <table id="example2" class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th style="width: 50px;">#</th>
                                        <th>Department Code</th>
                                        <th>Department Name</th>
                                        <th>Division</th>
                                        <th style="width: 150px;" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for department in departments %}
                                    <tr>
                                        <td>
                                            <span class="badge badge-secondary">{{ forloop.counter }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">{{ department.code }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="mr-3">
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                        <i class="fas fa-building text-white"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong>{{ department.name }}</strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-code mr-1"></i>{{ department.code }}
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-success">
                                                <i class="fas fa-sitemap mr-1"></i>{{ department.division }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'manage_department' department.id %}" class="btn btn-info btn-sm" title="Edit Department">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'delete_department' department.id %}" class="btn btn-danger btn-sm"
                                                   title="Delete Department" data-confirm="Are you sure you want to delete this department?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-building fa-3x mb-3"></i>
                                                <h5>No Departments Found</h5>
                                                <p>Start by adding your first department.</p>
                                                <a href="{% url 'manage_department' %}" class="btn btn-primary">
                                                    <i class="fas fa-plus mr-2"></i>Add Department
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}

