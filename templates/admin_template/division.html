{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}

{% block content %}

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- Enhanced Division Management Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-sitemap mr-2"></i>{{ page_title }}
                        </h3>
                        <div class="card-tools">
                            <div class="btn-group">
                                <a href="{% url 'manage_division' %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus mr-1"></i> Add New Division
                                </a>
                                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown">
                                    <span class="sr-only">Toggle Dropdown</span>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{% url 'division' %}">
                                        <i class="fas fa-list mr-2"></i>All Divisions
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- Enhanced DataTable -->
                        <div class="table-responsive">
                            <table id="example1" class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th style="width: 50px;">#</th>
                                        <th>Division Name</th>
                                        <th style="width: 150px;" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for division in divisions %}
                                    <tr>
                                        <td>
                                            <span class="badge badge-secondary">{{ forloop.counter }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="mr-3">
                                                    <div class="bg-info rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                        <i class="fas fa-sitemap text-white"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong>{{ division.name }}</strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-layer-group mr-1"></i>Organizational Division
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'manage_division' division.id %}" class="btn btn-info btn-sm" title="Edit Division">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'delete_division' division.id %}" class="btn btn-danger btn-sm"
                                                   title="Delete Division" data-confirm="Are you sure you want to delete this division?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="3" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-sitemap fa-3x mb-3"></i>
                                                <h5>No Divisions Found</h5>
                                                <p>Start by adding your first division.</p>
                                                <a href="{% url 'manage_division' %}" class="btn btn-primary">
                                                    <i class="fas fa-plus mr-2"></i>Add Division
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}


