{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}
{% block content %}

<!-- Main content -->
<section class="content">
    <form role="form" method="POST" enctype="multipart/form-data" action="{% url 'add_staff' %}">
        {% csrf_token %}
        <div class="row">
            <div class="col-md-6">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title">General</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="id_first_name">First Name:</label>
                            <input type="text" name="first_name" class="form-control {% if form.first_name.errors %}is-invalid{% endif %}" id="id_first_name" value="{{ form.first_name.value|default_if_none:'' }}">
                            {% for error in form.first_name.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_last_name">Last Name:</label>
                            <input type="text" name="last_name" class="form-control {% if form.last_name.errors %}is-invalid{% endif %}" id="id_last_name" value="{{ form.last_name.value|default_if_none:'' }}">
                            {% for error in form.last_name.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_email">Email:</label>
                            <input type="email" name="email" maxlength="320" class="form-control {% if form.email.errors %}is-invalid{% endif %}" id="id_email" value="{{ form.email.value|default_if_none:'' }}">
                            {% for error in form.email.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_gender">Gender:</label>
                            <select name="gender" class="form-control select2bs4 {% if form.gender.errors %}is-invalid{% endif %}" id="id_gender">
                                <option value="">Select One</option>
                                {% for key, value in gender %}
                                <option value="{{ key }}" {% if form.gender.value == key %}selected{% endif %}>{{ value }}</option>
                                {% endfor %}
                            </select>
                            {% for error in form.gender.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_father_name">Father/Husband's Name:</label>
                            <input type="text" name="father_name" class="form-control {% if form.father_name.errors %}is-invalid{% endif %}" id="id_father_name" value="{{ form.father_name.value|default_if_none:'' }}">
                            {% for error in form.father_name.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_profile_pic">Profile Photo:</label>
                            <input type="file" name="profile_pic" accept="image/*" class="form-control-file {% if form.profile_pic.errors %}is-invalid{% endif %}" id="id_profile_pic">
                            {% for error in form.profile_pic.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_employment_type">Employment Type:</label>
                            <select name="employment_type" class="form-control select2bs4 {% if form.employment_type.errors %}is-invalid{% endif %}" id="id_employment_type">
                                <option value="">Select One</option>
                                {% for key, value in Emp_choices %}
                                <option value="{{ key }}" {% if form.employment_type.value == key %}selected{% endif %}>{{ value }}</option>
                                {% endfor %}
                            </select>
                            {% for error in form.employment_type.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_emp_doj">Date of Joining:</label>
                            <input type="date" name="emp_doj" class="form-control {% if form.emp_doj.errors %}is-invalid{% endif %}" id="id_emp_doj" value="{{ form.emp_doj.value|default_if_none:'' }}">
                            {% for error in form.emp_doj.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card card-secondary">
                    <div class="card-header">
                        <h3 class="card-title">Professional</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="id_division">Division:</label>
                            <select name="division" class="form-control select2bs4 {% if form.division.errors %}is-invalid{% endif %}" id="id_division">
                                <option value="">Select One</option>
                                {% for x in divisions %}
                                <option value="{{ x.id }}" {% if form.division.value == x.id %}selected{% endif %}>{{ x.name }}</option>
                                {% endfor %}
                            </select>
                            {% for error in form.division.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_department">Department:</label>
                            <select name="department" class="form-control select2bs4 {% if form.department.errors %}is-invalid{% endif %}" id="id_department">
                                <option value="">Select One</option>
                                {% if form.instance.division %}
                                {% for department in form.instance.division.departments.all %}
                                <option value="{{ department.id }}" {% if department == form.instance.department %} selected {% endif %}>{{ department.name }}</option>
                                {% endfor %}
                                {% endif %}
                            </select>
                            {% for error in form.department.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_designation">Designation:</label>
                            <select name="designation" class="form-control select2bs4 {% if form.designation.errors %}is-invalid{% endif %}" id="id_designation">
                                <option value="">Select One</option>
                                {% if form.instance.department %}
                                {% for designation in form.instance.department.designations.all %}
                                <option value="{{ designation.id }}" {% if designation == form.instance.designation %} selected {% endif %}>{{ designation.name }}</option>
                                {% endfor %}
                                {% endif %}
                            </select>
                            {% for error in form.designation.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_emp_code">Employee Code:</label>
                            <input type="text" name="emp_code" class="form-control {% if form.emp_code.errors %}is-invalid{% endif %}" id="id_emp_code" value="{{ form.emp_code.value|default_if_none:'' }}">
                            {% for error in form.emp_code.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_grade">Grade Level:</label>
                            <select name="grade" class="form-control select2bs4 {% if form.grade.errors %}is-invalid{% endif %}" id="id_grade">
                                <option value="">Select One</option>
                                {% for x in grades %}
                                <option value="{{ x.id }}" {% if form.grade.value == x.id %}selected{% endif %}>{{ x.name }}</option>
                                {% endfor %}
                            </select>
                            {% for error in form.grade.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_basic_amt">Basic Salary Amount (₹):</label>
                            <input type="text" name="basic_amt" class="form-control {% if form.basic_amt.errors %}is-invalid{% endif %}" id="id_basic_amt" value="{{ form.basic_amt.value|default_if_none:'' }}">
                            {% for error in form.basic_amt.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_cca">CCA (₹):</label>
                            <input type="text" name="cca" class="form-control {% if form.cca.errors %}is-invalid{% endif %}" id="id_cca" value="{{ form.cca.value|default_if_none:'' }}">
                            {% for error in form.cca.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-group">
                            <label for="id_uan">UAN/PF A/c:</label>
                            <input type="text" name="uan" class="form-control {% if form.uan.errors %}is-invalid{% endif %}" id="id_uan" value="{{ form.uan.value|default_if_none:'' }}">
                            {% for error in form.uan.errors %}
                            <div class="invalid-feedback">
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <a href="javascript:history.back()" class="btn btn-secondary">Cancel</a>
                <input type="submit" value="Add Staff" class="btn btn-success float-right">
            </div>
        </div>
    </form>
</section>

<!-- /.content -->
{% endblock content %}

{% block extra_js %}
<script>
    function validateEmail(email) {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    }

    $(document).ready(function () {
        $("#id_email").keyup(function () {
            var email = $(this).val();
            if (validateEmail(email)) {
                $.ajax({
                    url: "{% url 'check_email_availability' %}",
                    type: 'POST',
                    data: { email: email },
                    headers: {
                        'X-CSRFToken': $('input[name=csrfmiddlewaretoken]').val()
                    },
                }).done(function (response) {
                    $(".email_error").show(); // Always show the message container
                    if (response == "True") {
                        $(".email_error").text("Email Address Already Exists").removeClass("valid").addClass("text-danger");
                        $("#id_email").removeClass("is-valid").addClass("is-invalid");
                    } else {
                        $(".email_error").text("Email Address Available").removeClass("text-danger").addClass("text-success");
                        $("#id_email").removeClass("is-invalid").addClass("is-valid");
                    }
                }).fail(function (response) {
                    $(".email_error").text("Server Could Not Process This").removeClass("valid").addClass("text-danger");
                    $("#id_email").removeClass("is-valid").addClass("is-invalid");
                });
            } else {
                $(".email_error").hide(); // Hide the message container if email is invalid
                $("#id_email").removeClass("is-valid is-invalid");
            }
        });

        $('#id_division').on('change', function () {
            let divisionId = $(this).val();
            let departmentSelect = $('#id_department');
            let designationSelect = $('#id_designation');

            departmentSelect.empty();
            designationSelect.empty();

            if (!divisionId) {
                return; // Do nothing if no division is selected
            }

            $.ajax({
                url: "{% url 'get_dep_by_div' %}",
                data: { 'division_id': divisionId },
                success: function (data) {
                    if (data.length > 0) {
                        departmentSelect.append($('<option></option>').attr('value', '').text('Select One'));
                        $.each(data, function (index, department) {
                            departmentSelect.append($('<option></option>').attr('value', department.id).text(department.name));
                        });
                    } else {
                        departmentSelect.append($('<option></option>').attr('value', '').text('No Department Available'));
                    }
                },
                error: function () {
                    departmentSelect.append($('<option></option>').attr('value', '').text('Error fetching departments'));
                }
            });
        });

        $('#id_department').on('change', function () {
            let departmentId = $(this).val();
            let designationSelect = $('#id_designation');

            designationSelect.empty();

            if (!departmentId) {
                return; // Do nothing if no department is selected
            }

            $.ajax({
                url: "{% url 'get_des_by_dep' %}",
                data: { 'department_id': departmentId },
                success: function (data) {
                    if (data.length > 0) {
                        designationSelect.append($('<option></option>').attr('value', '').text('Select One'));
                        $.each(data, function (index, designation) {
                            designationSelect.append($('<option></option>').attr('value', designation.id).text(designation.name));
                        });
                    } else {
                        designationSelect.append($('<option></option>').attr('value', '').text('No Designation Available'));
                    }
                },
                error: function () {
                    designationSelect.append($('<option></option>').attr('value', '').text('Error fetching designations'));
                }
            });
        });
    });
</script>
{% endblock extra_js %}