{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}

{% block content %}

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- Enhanced Staff Management Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-users mr-2"></i>{{ page_title }}
                        </h3>
                        <div class="card-tools">
                            <div class="btn-group">
                                <a href="{% url 'add_staff' %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-user-plus mr-1"></i> Add New Staff
                                </a>
                                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown">
                                    <span class="sr-only">Toggle Dropdown</span>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{% url 'manage_staff' %}">
                                        <i class="fas fa-list mr-2"></i>All Staff
                                    </a>
                                    <a class="dropdown-item" href="{% url 'manage_staff' %}?is_active=True">
                                        <i class="fas fa-user-check mr-2"></i>Active Staff
                                    </a>
                                    <a class="dropdown-item" href="{% url 'manage_staff' %}?is_active=False">
                                        <i class="fas fa-user-times mr-2"></i>Inactive Staff
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Enhanced DataTable -->
                        <div class="table-responsive">
                            <table id="example1" class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th style="width: 50px;">#</th>
                                        <th>Full Name</th>
                                        <th>Email</th>
                                        <th>Gender</th>
                                        <th>Department</th>
                                        <th>Designation</th>
                                        <th>Employee Type</th>
                                        <th style="width: 80px;">Avatar</th>
                                        <th style="width: 150px;" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for staff in staffs %}
                                    <tr>
                                        <td>
                                            <span class="badge badge-secondary">{{ forloop.counter }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                    <strong>{{ staff.first_name }} {{ staff.last_name }}</strong>
                                                    {% if staff.is_active %}
                                                    <span class="badge badge-success badge-sm ml-1">Active</span>
                                                    {% else %}
                                                    <span class="badge badge-danger badge-sm ml-1">Inactive</span>
                                                    {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <a href="mailto:{{ staff.email }}" class="text-primary">
                                                <i class="fas fa-envelope mr-1"></i>{{ staff.email }}
                                            </a>
                                        </td>
                                        <td>
                                            {% if staff.gender == 'Male' %}
                                            <span class="badge badge-info">
                                                <i class="fas fa-mars mr-1"></i>{{ staff.gender }}
                                            </span>
                                            {% else %}
                                            <span class="badge badge-pink">
                                                <i class="fas fa-venus mr-1"></i>{{ staff.gender }}
                                            </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">{{ staff.staff.department }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-success">{{ staff.staff.designation }}</span>
                                        </td>
                                        <td>
                                            {% if staff.staff.employment_type == 'Regular' %}
                                            <span class="badge badge-success">{{ staff.staff.employment_type }}</span>
                                            {% elif staff.staff.employment_type == 'Contract' %}
                                            <span class="badge badge-warning">{{ staff.staff.employment_type }}</span>
                                            {% else %}
                                            <span class="badge badge-info">{{ staff.staff.employment_type }}</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            {% if staff.profile_pic %}
                                            <img class="img-circle elevation-2" style="width: 50px; height: 50px;"
                                                src="{{ staff.profile_pic }}" alt="Staff Avatar">
                                            {% else %}
                                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 50px; height: 50px;">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'edit_staff' staff.staff.id %}" class="btn btn-info btn-sm" title="Edit Staff">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                {% if staff.is_active == 1 %}
                                                <a href="{% url 'delete_staff' staff.staff.id %}" class="btn btn-danger btn-sm"
                                                   title="Delete Staff" data-confirm="Are you sure you want to delete this staff member?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="9" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <h5>No Staff Members Found</h5>
                                                <p>Start by adding your first staff member.</p>
                                                <a href="{% url 'add_staff' %}" class="btn btn-primary">
                                                    <i class="fas fa-user-plus mr-2"></i>Add Staff Member
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}