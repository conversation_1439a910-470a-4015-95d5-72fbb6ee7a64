{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}

{% block content %}

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- Enhanced Accountant Management Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user-tie mr-2"></i>{{ page_title }}
                        </h3>
                        <div class="card-tools">
                            <div class="btn-group">
                                <a href="{% url 'add_accountant' %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-user-plus mr-1"></i> Add New Accountant
                                </a>
                                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown">
                                    <span class="sr-only">Toggle Dropdown</span>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{% url 'manage_accountant' %}">
                                        <i class="fas fa-list mr-2"></i>All Accountants
                                    </a>
                                    <a class="dropdown-item" href="{% url 'manage_accountant' %}?is_active=True">
                                        <i class="fas fa-user-check mr-2"></i>Active Accountants
                                    </a>
                                    <a class="dropdown-item" href="{% url 'manage_accountant' %}?is_active=False">
                                        <i class="fas fa-user-times mr-2"></i>Inactive Accountants
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Enhanced DataTable -->
                        <div class="table-responsive">
                            <table id="example1" class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th style="width: 50px;">#</th>
                                        <th>Full Name</th>
                                        <th>Email</th>
                                        <th>Gender</th>
                                        <th style="width: 80px;">Avatar</th>
                                        <th style="width: 150px;" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for accountant in allAccountant %}
                                    <tr>
                                        <td>
                                            <span class="badge badge-secondary">{{ forloop.counter }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">

                                                    <strong>{{ accountant.first_name }} {{ accountant.last_name }}</strong>
                                                    {% if accountant.is_active %}
                                                    <span class="badge badge-success badge-sm ml-1">Active</span>
                                                    {% else %}
                                                    <span class="badge badge-danger badge-sm ml-1">Inactive</span>
                                                    {% endif %}

                                        </td>
                                        <td>
                                            <a href="mailto:{{ accountant.email }}" class="text-primary">
                                                <i class="fas fa-envelope mr-1"></i>{{ accountant.email }}
                                            </a>
                                        </td>
                                        <td>
                                            {% if accountant.gender == 'Male' %}
                                            <span class="badge badge-info">
                                                <i class="fas fa-mars mr-1"></i>{{ accountant.gender }}
                                            </span>
                                            {% else %}
                                            <span class="badge badge-pink">
                                                <i class="fas fa-venus mr-1"></i>{{ accountant.gender }}
                                            </span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            {% if accountant.profile_pic %}
                                            <img class="img-circle elevation-2" style="width: 50px; height: 50px;"
                                                src="{{ accountant.profile_pic }}" alt="Accountant Avatar">
                                            {% else %}
                                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 50px; height: 50px;">
                                                <i class="fas fa-user-tie text-white"></i>
                                            </div>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'edit_accountant' accountant.accountant.id %}" class="btn btn-info btn-sm" title="Edit Accountant">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'delete_accountant' accountant.accountant.id %}" class="btn btn-danger btn-sm"
                                                   title="Delete Accountant" data-confirm="Are you sure you want to delete this accountant?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-user-tie fa-3x mb-3"></i>
                                                <h5>No Accountants Found</h5>
                                                <p>Start by adding your first accountant.</p>
                                                <a href="{% url 'add_accountant' %}" class="btn btn-primary">
                                                    <i class="fas fa-user-plus mr-2"></i>Add Accountant
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}
