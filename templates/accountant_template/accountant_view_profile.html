{% extends 'main_app/base.html' %}
{% load static %}
{% load custom_filters %}
{% block page_title %}My Profile{% endblock page_title %}

{% block extra_css %}
<style>
  .profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 30px;
    position: relative;
    overflow: hidden;
  }
  .profile-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
  }
  @keyframes float {
    0% { transform: translateX(-100px) translateY(-100px); }
    100% { transform: translateX(100px) translateY(100px); }
  }
  .profile-avatar {
    width: 120px;
    height: 120px;
    border: 5px solid white;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    position: relative;
    z-index: 2;
  }
  .profile-card {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-radius: 15px;
    overflow: hidden;
  }
  .info-item {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
  }
  .info-item:hover {
    background-color: #f8f9fa;
  }
  .info-item:last-child {
    border-bottom: none;
  }
  .info-label {
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
  }
  .info-value {
    color: #6c757d;
    font-weight: 500;
  }
  .stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: transform 0.3s ease;
  }
  .stats-card:hover {
    transform: translateY(-5px);
  }
  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
  }
  .stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 5px;
  }
  .activity-timeline {
    position: relative;
    padding-left: 30px;
  }
  .activity-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #007bff;
  }
  .activity-item {
    position: relative;
    margin-bottom: 20px;
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  }
  .activity-item::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #007bff;
    border: 3px solid white;
  }
  .edit-profile-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 25px;
    padding: 10px 30px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
  }
  .edit-profile-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    color: white;
  }
</style>
{% endblock extra_css %}

{% block content %}

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <!-- Profile Information -->
            <div class="col-md-8">
                <div class="card profile-card">
                    <div class="profile-header text-center">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                {% if user.profile_pic %}
                                  <img class="profile-avatar img-fluid rounded-circle"
                                       src="{{ user.profile_pic}}"
                                       alt="User profile picture">
                                {% else %}
                                  <div class="profile-avatar rounded-circle bg-white d-flex align-items-center justify-content-center mx-auto">
                                    <i class="fas fa-user fa-3x text-primary"></i>
                                  </div>
                                {% endif %}
                            </div>
                            <div class="col-md-8 text-left">
                                <h2 class="mb-1">{{ user.first_name }} {{ user.last_name }}</h2>
                                <p class="mb-2 opacity-75">
                                    <i class="fas fa-briefcase mr-2"></i>Payroll Accountant
                                </p>
                                <p class="mb-3 opacity-75">
                                    <i class="fas fa-envelope mr-2"></i>{{ user.email }}
                                </p>
                                <button class="btn edit-profile-btn">
                                    <i class="fas fa-edit mr-2"></i>Edit Profile
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <h5 class="mb-4">
                            <i class="fas fa-info-circle mr-2 text-primary"></i>
                            Personal Information
                        </h5>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-user mr-2 text-primary"></i>
                                        Full Name
                                    </div>
                                    <div class="info-value mt-1">{{ user.first_name }} {{ user.last_name }}</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-envelope mr-2 text-primary"></i>
                                        Email Address
                                    </div>
                                    <div class="info-value mt-1">{{ user.email }}</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-venus-mars mr-2 text-primary"></i>
                                        Gender
                                    </div>
                                    <div class="info-value mt-1">{{ user.gender|default:"Not specified" }}</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-user-tie mr-2 text-primary"></i>
                                        Father's Name
                                    </div>
                                    <div class="info-value mt-1">{{ user.father_name|default:"Not specified" }}</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-calendar-alt mr-2 text-primary"></i>
                                        Member Since
                                    </div>
                                    <div class="info-value mt-1">{{ user.date_joined|date:"F d, Y" }}</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-clock mr-2 text-primary"></i>
                                        Last Login
                                    </div>
                                    <div class="info-value mt-1">{{ user.last_login|date:"F d, Y - g:i A"|default:"Never" }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics and Activity -->
            <div class="col-md-4">
                <!-- Quick Stats -->
                <div class="row mb-4">
                    <div class="col-12 mb-3">
                        <div class="stats-card">
                            <div class="stat-number">{{ user.accountant.created_at|timesince|split:" "|first }}</div>
                            <div class="stat-label">Days Active</div>
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <div class="stats-card">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">Profile Complete</div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history mr-2"></i>
                            Recent Activity
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="activity-timeline">
                            <div class="activity-item">
                                <h6 class="mb-1">Profile Viewed</h6>
                                <small class="text-muted">You viewed your profile</small>
                                <br><small class="text-info">Just now</small>
                            </div>
                            <div class="activity-item">
                                <h6 class="mb-1">Payroll Generated</h6>
                                <small class="text-muted">Generated monthly payroll</small>
                                <br><small class="text-info">2 hours ago</small>
                            </div>
                            <div class="activity-item">
                                <h6 class="mb-1">Login Activity</h6>
                                <small class="text-muted">Logged into system</small>
                                <br><small class="text-info">{{ user.last_login|timesince }} ago</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt mr-2"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{% url 'accountant_home' %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-tachometer-alt mr-2"></i>Go to Dashboard
                            </a>
                            <a href="{% url 'staff_list' %}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-users mr-2"></i>View Staff Directory
                            </a>
                            <a href="{% url 'payroll_summary_report' %}" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-chart-bar mr-2"></i>View Reports
                            </a>
                            <a href="{% url 'user_logout' %}" class="btn btn-outline-danger btn-sm" onclick="return confirm('Are you sure you want to logout?')">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add smooth animations
    $('.info-item').hover(
        function() {
            $(this).addClass('bg-light');
        },
        function() {
            $(this).removeClass('bg-light');
        }
    );

    // Edit profile button functionality
    $('.edit-profile-btn').click(function() {
        toastr.info('Profile editing functionality will be available soon!');
    });
});
</script>
{% endblock extra_js %}

