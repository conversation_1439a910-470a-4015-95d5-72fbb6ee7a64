{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Payroll Summary Report{% endblock page_title %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'plugins/chart.js/Chart.min.css' %}">
<style>
  .summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }
  .summary-stat {
    text-align: center;
    padding: 15px;
  }
  .summary-stat h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
  }
  .summary-stat p {
    opacity: 0.9;
    margin-bottom: 0;
  }
  .filter-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
  }
  .chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
  }
  .payroll-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  }
  .table-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
  }
  .export-buttons .btn {
    margin: 5px;
    border-radius: 20px;
    padding: 8px 20px;
    font-weight: 500;
  }
  .employee-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
  }
  .salary-amount {
    font-weight: 600;
    color: #28a745;
  }
  .deduction-amount {
    font-weight: 600;
    color: #dc3545;
  }
  .net-amount {
    font-weight: 700;
    color: #007bff;
    font-size: 1.1rem;
  }
  .summary-totals {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
  }
  .total-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
  }
  .total-item:last-child {
    border-bottom: none;
    font-size: 1.2rem;
    font-weight: bold;
  }
  .department-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
  }
</style>
{% endblock extra_css %}

{% block content %}

<section class="content">
   <div class="container-fluid">
      <!-- Quick Stats -->
      <div class="row mb-4">
         <div class="col-md-4">
            <div class="card text-center">
               <div class="card-body">
                  <i class="fas fa-money-bill-wave fa-3x text-success mb-3"></i>
                  <h4 class="salary-amount">₹{{ total_salary|floatformat:2 }}</h4>
                  <p class="text-muted">Total Gross Pay</p>
               </div>
            </div>
         </div>
         <div class="col-md-4">
            <div class="card text-center">
               <div class="card-body">
                  <i class="fas fa-minus-circle fa-3x text-danger mb-3"></i>
                  <h4 class="deduction-amount">₹{{ total_deductions|floatformat:2 }}</h4>
                  <p class="text-muted">Total Deductions</p>
               </div>
            </div>
         </div>
         <div class="col-md-4">
            <div class="card text-center">
               <div class="card-body">
                  <i class="fas fa-hand-holding-usd fa-3x text-primary mb-3"></i>
                  <h4 class="net-amount">₹{{ total_salary|add:total_deductions|floatformat:2 }}</h4>
                  <p class="text-muted">Net Payable</p>
               </div>
            </div>
         </div>
      </div>

      <!-- Filters and Charts Row -->
      <div class="row">
         <div class="col-md-8">
            <!-- Chart Section -->
            <div class="card">
               <div class="card-header">
                  <h5 class="card-title">
                     <i class="fas fa-chart-bar mr-2"></i>
                     Payroll Distribution
                  </h5>
               </div>
               <div class="card-body">
                  <div class="chart-container">
                     <canvas id="payrollChart"></canvas>
                  </div>
               </div>
            </div>
         </div>

         <div class="col-md-4">
            <!-- Filters -->
            <div class="filter-section">
               <h6 class="mb-3">
                  <i class="fas fa-filter mr-2"></i>
                  Filter Options
               </h6>
               <div class="form-group">
                  <label for="employment-type-filter">Employment Type:</label>
                  <select id="employment-type-filter" class="form-control">
                     <option value="">All Employee Types</option>
                     <option value="Regular" {% if employment_type == 'Regular' %}selected{% endif %}>Regular</option>
                     <option value="Contract" {% if employment_type == 'Contract' %}selected{% endif %}>Contract</option>
                     <option value="Active" {% if employment_type == 'Active' %}selected{% endif %}>Active</option>
                  </select>
               </div>
            </div>

            <!-- Export Options -->
            <div class="card">
               <div class="card-header">
                  <h6 class="card-title mb-0">
                     <i class="fas fa-download mr-2"></i>
                     Export Options
                  </h6>
               </div>
               <div class="card-body export-buttons text-center">
                  <a href="{% url 'download_payroll_summary' month year %}" class="btn btn-success btn-sm">
                     <i class="fas fa-file-excel mr-2"></i>Excel
                  </a>
                  <button class="btn btn-danger btn-sm" onclick="exportToPDF()">
                     <i class="fas fa-file-pdf mr-2"></i>PDF
                  </button>
                  <button class="btn btn-info btn-sm" onclick="window.print()">
                     <i class="fas fa-print mr-2"></i>Print
                  </button>
               </div>
            </div>
         </div>
      </div>

      <!-- Detailed Table -->
      <div class="row">
         <div class="col-12">
            <div class="payroll-table">
               <div class="table-header">
                  <div class="row align-items-center">
                     <div class="col-md-8">
                        <h5 class="mb-0">
                           <i class="fas fa-table mr-2"></i>
                           Detailed Payroll Breakdown
                        </h5>
                     </div>
                     <div class="col-md-4 text-right">
                        <small class="text-muted">{{ payslips|length }} employees</small>
                     </div>
                  </div>
               </div>

               <div class="card-body">
                  <div class="table-responsive">
                     <table id="example1" class="table table-hover mb-0">
                        <thead class="thead-light">
                           <tr>
                              <th width="8%">Photo</th>
                              <th width="12%">Emp Code</th>
                              <th width="20%">Employee Name</th>
                              <th width="15%">Department</th>
                              <th width="12%">Basic Salary</th>
                              <th width="12%">Gross Pay</th>
                              <th width="12%">Deductions</th>
                              <th width="12%">Net Pay</th>
                           </tr>
                        </thead>
                        <tbody>
                           {% for payslip in payslips %}
                           <tr>
                              <td>
                                 {% if payslip.staff.user.profile_pic %}
                                    <img src="{{ payslip.staff.user.profile_pic }}" alt="Profile" class="employee-avatar">
                                 {% else %}
                                    <div class="employee-avatar bg-primary d-flex align-items-center justify-content-center text-white">
                                       {{ payslip.staff.user.first_name|first }}{{ payslip.staff.user.last_name|first }}
                                    </div>
                                 {% endif %}
                              </td>
                              <td>
                                 <span class="badge badge-outline-primary">{{ payslip.staff.emp_code }}</span>
                              </td>
                              <td>
                                 <div class="font-weight-bold">{{ payslip.staff.user.get_full_name }}</div>
                                 <small class="text-muted">{{ payslip.staff.employment_type }}</small>
                              </td>
                              <td>
                                 <span class="badge department-badge badge-light">{{ payslip.staff.department.name }}</span>
                              </td>
                              <td class="salary-amount">₹{{ payslip.basic|floatformat:2 }}</td>
                              <td class="salary-amount">₹{{ payslip.gross_pay|floatformat:2 }}</td>
                              <td class="deduction-amount">₹{{ payslip.total_deductions|floatformat:2 }}</td>
                              <td class="net-amount">₹{{ payslip.net_pay|floatformat:2 }}</td>
                           </tr>
                           {% empty %}
                           <tr>
                              <td colspan="8" class="text-center py-4">
                                 <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                 <h5 class="text-muted">No payroll data found</h5>
                                 <p class="text-muted">Try adjusting your filters or generate payroll first.</p>
                              </td>
                           </tr>
                           {% endfor %}
                        </tbody>
                     </table>
                  </div>
               </div>
            </div>
         </div>
      </div>

      <!-- Summary Totals -->
      {% if payslips %}
      <div class="summary-totals">
         <div class="row">
            <div class="col-md-8">
               <h5 class="mb-3">
                  <i class="fas fa-calculator mr-2"></i>
                  Summary Totals
               </h5>
            </div>
            <div class="col-md-4">
               <div class="total-item">
                  <span>Total Gross Salary:</span>
                  <span>₹{{ total_salary|floatformat:2 }}</span>
               </div>
               <div class="total-item">
                  <span>Total Deductions:</span>
                  <span>₹{{ total_deductions|floatformat:2 }}</span>
               </div>
               <div class="total-item">
                  <span>Net Payable Amount:</span>
                  <span>₹{{ total_salary|add:total_deductions|floatformat:2 }}</span>
               </div>
            </div>
         </div>
      </div>
      {% endif %}
   </div>
</section>

{% endblock content %}

{% block extra_js %}
<script src="{% static 'plugins/chart.js/Chart.min.js' %}"></script>
<script>
$(document).ready(function() {
   // Initialize Chart
   const ctx = document.getElementById('payrollChart').getContext('2d');
   const payrollChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
         labels: ['Gross Pay', 'Deductions'],
         datasets: [{
            data: [{{ total_salary }}, {{ total_deductions }}],
            backgroundColor: ['#28a745', '#dc3545'],
            borderWidth: 0
         }]
      },
      options: {
         responsive: true,
         maintainAspectRatio: false,
         plugins: {
            legend: {
               position: 'bottom'
            }
         }
      }
   });
});

function applyFilters() {
   let employmentType = $('#employment-type-filter').val();
   let month = $('#month-filter').val();
   let url = "{% url 'payroll_summary_report' %}";
   let params = [];

   if (employmentType) {
      params.push("employment_type=" + employmentType);
   }
   if (month) {
      params.push("month=" + month);
   }

   if (params.length > 0) {
      url += "?" + params.join("&");
   }

   window.location.href = url;
}

function exportToPDF() {
   toastr.info('PDF export functionality will be available soon!');
}

// Employment type filter change
$('#employment-type-filter').change(function () {
   applyFilters();
});
</script>
{% endblock extra_js %}