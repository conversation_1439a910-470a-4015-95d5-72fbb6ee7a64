{% load static %}
{% load custom_filters %}

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card card-primary card-outline">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-edit mr-2"></i>{{ form_title|default:"Form Details" }}
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <form role="form" method="POST" enctype="multipart/form-data" id="mainForm">
                {% csrf_token %}
                <div class="card-body">
                    <div class="alert alert-info alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <h5><i class="icon fas fa-info"></i> Information!</h5>
                        Please fill in all required fields carefully. Fields marked with <span class="text-danger">*</span> are mandatory.
                    </div>

                    {% for field in form %}
                        <div class="form-group">
                            {% if field.name == 'is_active' %}
                                <div class="form-check">
                                    {{ field|add_class:"form-check-input" }}
                                    <label class="form-check-label" for="{{ field.id_for_label }}">
                                        <i class="fas fa-toggle-on mr-1"></i>{{ field.label }}
                                    </label>
                                </div>
                            {% else %}
                                <label for="{{ field.id_for_label }}" class="form-label">
                                    {% if field.field.required %}
                                    <span class="text-danger">*</span>
                                    {% endif %}
                                    {% if field.name == 'email' %}
                                    <i class="fas fa-envelope mr-1"></i>
                                    {% elif field.name == 'password' or 'password' in field.name %}
                                    <i class="fas fa-lock mr-1"></i>
                                    {% elif 'file' in field.name or 'image' in field.name or 'photo' in field.name %}
                                    <i class="fas fa-upload mr-1"></i>
                                    {% endif %}
                                    {{ field.label }}
                                </label>

                                {% if field.name == 'email' %}
                                <div class="input-group">
                                    {{ field|add_class:"form-control" }}
                                    <div class="input-group-append">
                                        <div class="input-group-text">
                                            <span class="fas fa-envelope"></span>
                                        </div>
                                    </div>
                                </div>
                                {% elif field.name == 'password' or 'password' in field.name %}
                                <div class="input-group">
                                    {{ field|add_class:"form-control" }}
                                    <div class="input-group-append">
                                        <div class="input-group-text">
                                            <span class="fas fa-lock"></span>
                                        </div>
                                    </div>
                                </div>
                                {% elif 'file' in field.name or 'image' in field.name or 'photo' in field.name or 'pic' in field.name %}
                                <div class="input-group">
                                    <div class="custom-file">
                                        {{ field|add_class:"custom-file-input" }}
                                        <label class="custom-file-label" for="{{ field.id_for_label }}">Choose file</label>
                                    </div>
                                </div>
                                {% elif 'department' in field.name or 'designation' in field.name or 'division' in field.name or 'grade' in field.name or 'gender' in field.name or 'employment_type' in field.name %}
                                {{ field|add_class:"form-control select2bs4" }}
                                {% else %}
                                {{ field|add_class:"form-control" }}
                                {% endif %}
                            {% endif %}

                            {% if field.help_text %}
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle mr-1"></i>{{ field.help_text|safe }}
                                </small>
                            {% endif %}

                            {% if field.errors %}
                                <div class="invalid-feedback d-block">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>{{ field.errors.0 }}
                                </div>
                                <script>
                                    $(document).ready(function() {
                                        $('#{{ field.id_for_label }}').addClass('is-invalid');
                                    });
                                </script>
                            {% endif %}

                            {% if field.name == 'email' %}
                                <span class="error email_error text-danger" style="display:none;">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                </span>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>

                <div class="card-footer bg-light">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="javascript:history.back()" class="btn btn-secondary">
                                <i class="fas fa-arrow-left mr-2"></i>Cancel
                            </a>
                        </div>
                        <div class="col-md-6 text-right">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save mr-2"></i>{{ button_text|default:"Save" }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Enhanced Form JavaScript -->
<script>
$(document).ready(function() {
    // Custom file input label update
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).siblings('.custom-file-label').addClass('selected').html(fileName);
    });

    // Form validation enhancement
    $('#mainForm').on('submit', function(e) {
        var isValid = true;

        // Remove previous validation classes
        $('.form-control').removeClass('is-invalid is-valid');

        // Check required fields
        $('.form-control[required]').each(function() {
            if ($(this).val() === '') {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).addClass('is-valid');
            }
        });

        // Email validation
        var emailField = $('input[type="email"]');
        if (emailField.length > 0 && emailField.val() !== '') {
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(emailField.val())) {
                emailField.addClass('is-invalid');
                $('.email_error').text('Please enter a valid email address.').show();
                isValid = false;
            } else {
                emailField.addClass('is-valid');
                $('.email_error').hide();
            }
        }

        if (!isValid) {
            e.preventDefault();
            toastr.error('Please fill in all required fields correctly.');
            return false;
        }

        // Show loading
        showLoading();
    });

    // Real-time validation
    $('.form-control').on('blur', function() {
        if ($(this).attr('required') && $(this).val() === '') {
            $(this).addClass('is-invalid');
        } else if ($(this).val() !== '') {
            $(this).removeClass('is-invalid').addClass('is-valid');
        }
    });

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
</script>

