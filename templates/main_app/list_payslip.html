{% extends 'main_app/base.html' %}
{% load static %}
{% block page_title %}{{page_title}}{% endblock page_title %}

{% block content %}

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- Enhanced Payslip Management Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-file-invoice-dollar mr-2"></i>{{ page_title }}
                        </h3>
                        <div class="card-tools">
                            <div class="btn-group">
                                <button type="button" class="btn btn-info btn-sm dropdown-toggle" data-toggle="dropdown">
                                    <i class="fas fa-filter mr-1"></i> Filter Options
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{% url 'list_payslip' %}">
                                        <i class="fas fa-list mr-2"></i>All Payslips
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-calendar mr-2"></i>This Month
                                    </a>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-calendar-alt mr-2"></i>Last Month
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form id="send-email-form" method="post" action="{% url 'send_bulk_email' %}">
                        {% csrf_token %}

                        <div class="card-body">
                            <!-- Enhanced DataTable -->
                            <div class="table-responsive">
                                <table id="example1" class="table table-bordered table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th style="width: 40px;">
                                                <div class="icheck-primary">
                                                    <input type="checkbox" id="select_all_checkbox">
                                                    <label for="select_all_checkbox"></label>
                                                </div>
                                            </th>
                                            <th>Employee</th>
                                            <th>Department</th>
                                            <th>Month</th>
                                            <th>Basic Salary</th>
                                            <th>Gross Pay</th>
                                            <th>Deductions</th>
                                            <th>Net Pay</th>
                                            <th style="width: 150px;" class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for staff in staffs %}
                                        {% for payslip in staff.payslip_history %}
                                        <tr>
                                            <td>
                                                <div class="icheck-primary">
                                                    <input type="checkbox" class="staff-checkbox" name="staffs"
                                                           id="checkbox_{{ staff.id }}_{{ payslip.month|date:'Y-m-d' }}"
                                                           value="{{ staff.id }}-{{ payslip.month|date:'Y-m-d' }}">
                                                    <label for="checkbox_{{ staff.id }}_{{ payslip.month|date:'Y-m-d' }}"></label>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="mr-3">
                                                        {% if staff.user.profile_pic %}
                                                        <img class="img-circle elevation-2" style="width: 35px; height: 35px;"
                                                            src="{{ staff.user.profile_pic }}" alt="Staff Avatar">
                                                        {% else %}
                                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                        {% endif %}
                                                    </div>
                                                    <div>
                                                        <strong>{{ staff.user.first_name }} {{ staff.user.last_name }}</strong>
                                                        <br>
                                                        <small class="text-muted">
                                                            <i class="fas fa-id-card mr-1"></i>{{ staff.emp_code }}
                                                        </small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-primary">
                                                    <i class="fas fa-building mr-1"></i>{{ staff.department.name }}
                                                </span>
                                                <br>
                                                <small class="text-muted">{{ staff.designation.name }}</small>
                                            </td>
                                            <td>
                                                <span class="badge badge-info">
                                                    <i class="fas fa-calendar mr-1"></i>{{ payslip.month|date:"F Y" }}
                                                </span>
                                            </td>
                                            <td class="text-right">
                                                <span class="badge badge-success">
                                                    <i class="fas fa-rupee-sign mr-1"></i>{{ payslip.basic|floatformat:0 }}
                                                </span>
                                            </td>
                                            <td class="text-right">
                                                <span class="badge badge-primary">
                                                    <i class="fas fa-money-bill-wave mr-1"></i>{{ payslip.gross_pay|floatformat:0 }}
                                                </span>
                                            </td>
                                            <td class="text-right">
                                                <span class="badge badge-warning">
                                                    <i class="fas fa-minus mr-1"></i>{{ payslip.total_deductions|floatformat:0 }}
                                                </span>
                                            </td>
                                            <td class="text-right">
                                                <span class="badge badge-success badge-lg">
                                                    <i class="fas fa-hand-holding-usd mr-1"></i>{{ payslip.net_pay|floatformat:0 }}
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group" role="group">
                                                    <a href="{% url 'view_payslip' staff.id payslip.month|date:'Y-m-d' %}"
                                                       class="btn btn-success btn-sm" title="View Payslip">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-primary btn-sm send-email-btn" title="Send Email">
                                                        <i class="fas fa-envelope"></i>
                                                    </button>
                                                </div>
                                                <input type="hidden" name="month" class="payslip-month"
                                                       value="{{ payslip.month|date:'Y-m-d' }}">
                                                <input type="hidden" name="staff" class="staff-id" value="{{ staff.id }}">
                                            </td>
                                        </tr>
                                        {% endfor %}
                                        {% empty %}
                                        <tr>
                                            <td colspan="9" class="text-center text-muted">
                                                <div class="py-4">
                                                    <i class="fas fa-file-invoice-dollar fa-3x mb-3"></i>
                                                    <h5>No Payslips Found</h5>
                                                    <p>No payslips have been generated yet.</p>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card-footer bg-light">
                            <div class="row">
                                <div class="col-md-6">
                                    {% if user.user_type == 1 %}
                                    <button type="button" class="btn btn-warning" id="edit-payslip">
                                        <i class="fas fa-edit mr-2"></i> Edit Selected Payslips
                                    </button>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 text-right">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-paper-plane mr-2"></i> Send to Selected
                                    </button>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        Select payslips using checkboxes to perform bulk operations.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}

{% block extra_js %}
<script>
    $(document).ready(function () {
        // Function to get CSRF token from cookies
        function getCookie(name) {
            var cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                var cookies = document.cookie.split(';');
                for (var i = 0; i < cookies.length; i++) {
                    var cookie = jQuery.trim(cookies[i]);
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Handle individual Payslip Email Sending (`.send-email-btn` Click):
        $(document).on('click', '.send-email-btn', function () {
            var staffId = $(this).siblings('.staff-id').val();
            var month = $(this).siblings('.payslip-month').val();
            var csrftoken = getCookie('csrftoken'); // Get CSRF token
            var button = $(this);
            $.ajax({
                url: '/send_email/' + staffId + '/' + month + '/',
                type: 'POST',
                headers: { 'X-CSRFToken': csrftoken }, // Include CSRF token in headers
                beforeSend: function () {
                    button.prop('disabled', true); // Disable the button
                    button.html('<i class="fas fa-spinner fa-spin"></i> Sending...');
                },
                success: function (response) {
                    if (response.success) {
                        toastr.success('Email sent successfully!'); // Use Toastr for success
                    } else {
                        toastr.error(response.error); // Use Toastr for error
                    }
                },
                error: function (xhr, status, error) {
                    toastr.error('An error occurred while sending the email.'); // Use Toastr for error
                },
                complete: function () {
                    button.prop('disabled', false); // Enable the button after the request is complete
                    button.html('<i class="fas fa-envelope"></i> Send');
                }
            });
        });

        // Handle Bulk Email Sending (`#send-email-form` Submit):
        $('#send-email-form').submit(function (event) {
            event.preventDefault();

            $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Sending...');

            var selectedStaff = $('.staff-checkbox:checked').map(function () {
                return {
                    staffId: $(this).val(),
                    month: $(this).closest('tr').find('.payslip-month').val()
                };
            }).get();

            if (selectedStaff.length === 0) {
                toastr.error('No staff selected!'); // Use Toastr for error
                $('#send-email-form button[type="submit"]').prop('disabled', false).text('Send Emails to Selected');
                return;
            }

            var csrftoken = getCookie('csrftoken');
            $.ajax({
                url: '/send_bulk_email/',
                type: 'POST',
                headers: { 'X-CSRFToken': csrftoken },
                data: JSON.stringify({ 'staffs': selectedStaff }),
                contentType: "application/json",
                success: function (response) {
                    if (response.success) {
                        toastr.success(response.message); // Use Toastr for success
                    } else {
                        if (typeof response.errors === 'object') {
                            for (const field in response.errors) {
                                toastr.error(response.errors[field]); // Use Toastr for error
                            }
                        } else {
                            toastr.error(response.errors);  // Use Toastr for error
                        }
                    }
                },
                error: function (xhr, status, error) {
                    toastr.error("An unexpected error occurred.");  // Use Toastr for error
                },
                complete: function () {
                    $('#send-email-form button[type="submit"]').prop('disabled', false).text('Send Emails to Selected');
                }
            });

        });

        $('#edit-payslip').click(function () {

            var selectedStaff = $('.staff-checkbox:checked').map(function () {
                return {
                    staffId: $(this).val(),
                    month: $(this).closest('tr').find('.payslip-month').val()
                };
            }).get();

            if (selectedStaff.length === 0) {
                toastr.error('No staff selected!'); // Use Toastr for error
                return;
            }

            var csrftoken = getCookie('csrftoken');
            $.ajax({
                url: '/admin/edit/payslip/', // Correct URL for edit payslip
                type: 'POST',
                headers: { 'X-CSRFToken': csrftoken },
                data: JSON.stringify({ 'staffs': selectedStaff }),
                contentType: "application/json",
                success: function (response) {
                    if (response) {
                        var newWindow = window.open();
                        if (newWindow) {
                            newWindow.document.open();
                            newWindow.document.write(response);
                            newWindow.document.close();
                        } else {
                            toastr.error('Failed to open a new tab. Please check your pop-up settings.');
                        }
                    } else {
                        toastr.error('No response received from the server.');
                    }
                },
                error: function (xhr, status, error) {
                    toastr.error("An unexpected error occurred.");  // Use Toastr for error
                },
            });
        });

        $('#select_all_checkbox').change(function () {
            $('.staff-checkbox').prop('checked', this.checked);
        });
    });
</script>
{% endblock extra_js %}