{% load static %}
{% load custom_filters %}

<!DOCTYPE html>
<html lang="en">

<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <style>
      body {
         font-family: Arial, sans-serif;
         margin: 0;
         padding: 0;
      }

      .container {
         width: 80%;
         margin: auto;
         padding: 20px;

         box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
         border-radius: 10px;
      }

      .header {
         display: flex;
         align-items: center;
         justify-content: space-evenly;
         border-bottom: 2px solid #00afef;
         padding-bottom: 10px;
         margin-bottom: 20px;
      }

      .header img {
         height: 90px;
         margin-right: 50px
      }

      .header h2 {
         margin: 0;
         color: #00afef;
      }

      .header p {
         margin: 0;
         font-weight: bold;
         color: #555;
      }

      .details,
      .earnings,
      .summary,
      .reimbursements {
         width: 100%;
         margin-bottom: 20px;
         border-collapse: collapse;
         border: 1px solid #ddd;
         border-radius: 10px;
         overflow: hidden;
      }

      .details td,
      .earnings td,
      .summary td,
      .reimbursements td {
         padding: 8px;
         border: 2px solid #ddd;
         text-align: left;
         background-color: #f9f9f9;
         font-size: 0.83em;
      }

      .details th,
      .earnings th,
      .summary th,
      .reimbursements th {
         background: #00afef;
         color: #fff;
         padding: 10px;
         text-align: left;
         font-size: 0.85em;
      }

      .section-title {
         font-weight: bold;
         margin-bottom: 10px;
         border-bottom: 2px solid #00afef;
         padding-bottom: 5px;
         color: #00afef;
      }

      .net-pay {
         text-align: center;
         font-weight: bold;
         background: #00afef;
         color: #fff;
         padding: 10px;
         border-radius: 10px;
         font-size: 0.95em;
      }

      .note {
         text-align: center;
         color: #777;
      }

      .footer {
         text-align: center;
         margin-top: 20px;
         font-size: 0.8em;
         color: #777;
      }

      .footer p {
         margin: 10px 0;
         color: #888;
      }
   </style>
</head>

<body>
   <section>
      <div class="container">
         <div class="header">
            <table>
               <tr>
                  <td>
                     {% if logo == "ntc" %}
                     <img src="{% static 'img/ntc.png' %}" alt=" NITRA Logo">
                     {% else %}
                     <img src="{% static 'img/nitra.png' %}" alt="NTC Logo">
                     {% endif %}
                  </td>
                  <td>
                     <div>
                        <h2>Northern India Textile Research Association</h2>
                        <p>Ghaziabad</p>
                     </div>
                  </td>
               </tr>
            </table>
         </div>

         <table class="details">
            <tr>
               <th colspan="4">Employee Details</th>
            </tr>
            <tr>
               <td><strong>Employee Name:</strong></td>
               <td>{{ payroll_info.employee_name }}</td>
               <td><strong>Employee Code:</strong></td>
               <td>{{ payroll_info.employee_code }}</td>
            </tr>
            <tr>
               <td><strong>Designation:</strong></td>
               <td>{{ payroll_info.designation }}</td>
               <td><strong>Date of Joining:</strong></td>
               <td>{{payroll_info.doj}}</td>
            </tr>
            <tr>
               <td><strong>Department</strong></td>
               <td>{{ payroll_info.department }}</td>
               <td><strong>UAN/PF A/c:</strong></td>
               <td>{{ payroll_info.uan_pf_ac }}</td>
            </tr>
            <tr>
               <td><strong>Father/Husband Name:</strong></td>
               <td>{{ payroll_info.father_husband_name }}</td>
               <td><strong>Salary for the Month</strong></td>
               <td>{{ payroll_info.month }}</td>
            </tr>
         </table>

         <table class="summary">
            <tr>
               <th>Working Days</th>
               <td>{{ payroll_info.paid_days }}</td>
               <th>Paid Leaves</th>
               <td>{{payroll_info.lop}}</td>
               <th>LOP days</th>
               <td>{{payroll_info.leave}}</td>
            </tr>
         </table>

         <div class="section-title">Employee Pay Summary</div>
         <table class="earnings">
            <tr>
               <th>Earnings</th>
               <th>Amount (₹)</th>
               <th>Deductions</th>
               <th>Amount (₹)</th>
            </tr>
            <tr>
               <td>Basic</td>
               <td>{{ payroll_info.basic }}</td>
               <td>EPF</td>
               <td>{{ payroll_info.epf }}</td>
            </tr>
            <tr>
               <td>DP</td>
               <td>{{ payroll_info.dp }}</td>
               <td>Society</td>
               <td>{{ payroll_info.society }}</td>
            </tr>
            <tr>
               <td>Basic+DP</td>
               <td>{{ payroll_info.basic_dp }}</td>
               <td>ESI</td>
               <td>{{ payroll_info.esi }}</td>
            </tr>
            <tr>
               <td>DA</td>
               <td>{{ payroll_info.da }}</td>
               <td>IncomeTax</td>
               <td>{{ payroll_info.income_tax }}</td>
            </tr>
            <tr>
               <td>Basic+DP+DA</td>
               <td>{{ payroll_info.basic_dp_da }}</td>
               <td>Canteen</td>
               <td>{{ payroll_info.canteen }}</td>
            </tr>
            <tr>
               <td>HRA</td>
               <td>{{ payroll_info.hra }}</td>
               <td>Insurance</td>
               <td>{{ payroll_info.insurance }}</td>
            </tr>
            <tr>
               <td>CCA</td>
               <td>{{ payroll_info.cca }}</td>
               <td>Advance</td>
               <td>{{ payroll_info.advance }}</td>
            </tr>
            <tr>
               <td>Conv</td>
               <td>{{ payroll_info.conv }}</td>
               <td>Miscellaneous</td>
               <td>{{ payroll_info.Dother }}</td>
            </tr>
            <tr>
               <td>Adhoc</td>
               <td>{{ payroll_info.adhoc }}</td>
               <td></td>
               <td></td>
            </tr>
            <tr>
               <td>Medical</td>
               <td>{{ payroll_info.medical }}</td>
               <td></td>
               <td></td>
            </tr>
            <tr>
               <td>Arrears</td>
               <td>{{ payroll_info.arrears }}</td>
               <td></td>
               <td></td>
            </tr>
            <tr>
               <td>Advance</td>
               <td>{{ payroll_info.Pother }}</td>
               <td></td>
               <td></td>
            </tr>
            <tr>
               <th>Gross Pay</th>
               <th>{{payroll_info.gross_pay}}</th>
               <th>Total Deduction</th>
               <th>{{payroll_info.total_deduction}}</th>
            </tr>
         </table>

         <div class="net-pay">
            Total Net Payable: ₹ {{ payroll_info.net_pay }} <br>
            (Rupees {{ payroll_info.net_pay|number_to_words }} only)
         </div>

         <div class="footer">
            <p>- This is a system generated payslip -</p>
         </div>

      </div>
   </section>
</body>

</html>