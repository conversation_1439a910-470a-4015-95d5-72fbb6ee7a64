from django import template
from num2words import num2words

register = template.Library()

@register.filter
def number_to_words(value):
    try:
        value_in_words = num2words(value, lang='en')
    except Exception as e:
        value_in_words = str(e)
    return value_in_words

@register.filter
def add_class(field, css_class):
    """
    Add CSS class to form field widget
    Usage: {{ field|add_class:"form-control" }}
    """
    if hasattr(field, 'as_widget'):
        return field.as_widget(attrs={'class': css_class})
    return field

@register.filter
def add_attrs(field, attrs):
    """
    Add multiple attributes to form field widget
    Usage: {{ field|add_attrs:"class:form-control,placeholder:Enter value" }}
    """
    if not hasattr(field, 'as_widget'):
        return field

    attr_dict = {}
    if attrs:
        for attr in attrs.split(','):
            if ':' in attr:
                key, value = attr.split(':', 1)
                attr_dict[key.strip()] = value.strip()

    return field.as_widget(attrs=attr_dict)

@register.filter
def field_type(field):
    """
    Get the field widget type
    Usage: {% if field|field_type == 'EmailInput' %}
    """
    return field.field.widget.__class__.__name__

@register.filter
def split(value, delimiter):
    """
    Split a string by delimiter and return a list
    Usage: {{ "hello world"|split:" " }}
    """
    if value:
        return str(value).split(delimiter)
    return []
