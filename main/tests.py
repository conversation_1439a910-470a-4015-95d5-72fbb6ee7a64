from django.test import TestCase, Client, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone
from datetime import date, datetime, timedelta
from decimal import Decimal
import json

from .models import (
    CustomUser, Admin, Accountant, Staff, Division, Department,
    Designation, Grade, Fixed, Deductions, ContractPay, RegularPay,
    Attendance, Payslip
)
from .forms import LoginForm

# Test settings to bypass static files and middleware issues
TEST_SETTINGS = {
    'STATICFILES_STORAGE': 'django.contrib.staticfiles.storage.StaticFilesStorage',
    'MIDDLEWARE': [
        'django.middleware.security.SecurityMiddleware',
        'django.contrib.sessions.middleware.SessionMiddleware',
        'django.middleware.common.CommonMiddleware',
        'django.middleware.csrf.CsrfViewMiddleware',
        'django.contrib.auth.middleware.AuthenticationMiddleware',
        'django.contrib.messages.middleware.MessageMiddleware',
        'django.middleware.clickjacking.XFrameOptionsMiddleware',
        # Remove custom middleware for testing
    ],
    'GOOGLE_RECAPTCHA_SITE_KEY': 'test-key',
    'GOOGLE_RECAPTCHA_SECRET_KEY': 'test-secret',
}


@override_settings(**TEST_SETTINGS)
class BaseTestCase(TestCase):
    """Base test case with common setup for all tests"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()

        # Create test divisions
        self.division = Division.objects.create(name="Test Division")

        # Create test departments
        self.department = Department.objects.create(
            code="TD01",
            name="Test Department",
            division=self.division
        )

        # Create test designations
        self.designation = Designation.objects.create(
            name="Test Designation",
            department=self.department
        )

        # Create test grade
        self.grade = Grade.objects.create(
            name="Test Grade",
            start=10000,
            end=50000,
            increment=1000,
            medical=500,
            adhoc=1000,
            conva=500,
            cca=200
        )

        # Create test fixed values
        self.fixed = Fixed.objects.create(
            division=self.division,
            month=date.today().replace(day=1),
            da=0.15,
            hra=0.10
        )

        # Create test users
        self.admin_user = CustomUser.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Admin",
            last_name="User",
            father_name="Admin Father",
            user_type=1
        )

        self.accountant_user = CustomUser.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Accountant",
            last_name="User",
            father_name="Accountant Father",
            user_type=2
        )

        self.staff_user = CustomUser.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Staff",
            last_name="User",
            father_name="Staff Father",
            user_type=3
        )

        # Update staff with additional required fields
        self.staff = Staff.objects.get(user=self.staff_user)
        self.staff.division = self.division
        self.staff.department = self.department
        self.staff.designation = self.designation
        self.staff.emp_code = "EMP001"
        self.staff.uan = "************"
        self.staff.emp_doj = date.today() - timedelta(days=365)
        self.staff.grade = self.grade
        self.staff.basic_amt = 25000
        self.staff.employment_type = "Regular"
        self.staff.cca = 500
        self.staff.save()

        # Create test payslip
        self.payslip = Payslip.objects.create(
            staff=self.staff,
            month=date.today().replace(day=1),
            basic=25000,
            da=3750,
            dp=12500,
            hra=3750,
            conv=500,
            medical=0,
            cca=500,
            adhoc=1000,
            gross_pay=47000,
            epf=4950,
            esi=823,
            total_deductions=5773,
            net_pay=41227,
            paid_days=30,
            lop=0
        )


class AuthenticationTestCase(BaseTestCase):
    """Test authentication and user management"""

    def test_login_page_get(self):
        """Test login page renders correctly"""
        response = self.client.get(reverse('login_page'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'login')
        self.assertIsInstance(response.context['form'], LoginForm)

    def test_login_redirect_authenticated_user(self):
        """Test authenticated users are redirected from login page"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('login_page'))
        self.assertEqual(response.status_code, 302)

    def test_admin_login_success(self):
        """Test successful admin login"""
        response = self.client.post(reverse('user_login'), {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)
        # Should redirect to Django admin for admin users
        self.assertTrue(response.url.startswith('/admin/'))

    def test_accountant_login_success(self):
        """Test successful accountant login"""
        response = self.client.post(reverse('user_login'), {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accountant/home/', response.url)

    def test_staff_login_success(self):
        """Test successful staff login"""
        response = self.client.post(reverse('user_login'), {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)
        self.assertIn('/staff/home/', response.url)

    def test_login_invalid_credentials(self):
        """Test login with invalid credentials"""
        response = self.client.post(reverse('user_login'), {
            'email': '<EMAIL>',
            'password': 'wrongpass'
        })
        self.assertEqual(response.status_code, 302)
        # Should redirect back to login page
        self.assertIn('/', response.url)

    def test_logout(self):
        """Test user logout"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('user_logout'))
        self.assertEqual(response.status_code, 302)
        self.assertIn('/', response.url)


class CommonViewsTestCase(BaseTestCase):
    """Test common views accessible to all users"""

    def test_list_payslip_unauthenticated(self):
        """Test payslip list requires authentication"""
        response = self.client.get(reverse('list_payslip'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_list_payslip_authenticated(self):
        """Test payslip list for authenticated user"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('list_payslip'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'payslip')

    def test_list_payslip_by_type(self):
        """Test payslip list filtered by employment type"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('list_payslip_type', args=['Regular']))
        self.assertEqual(response.status_code, 200)

    def test_view_payslip_authenticated(self):
        """Test viewing specific payslip"""
        self.client.login(email='<EMAIL>', password='testpass123')
        month_str = self.payslip.month.strftime('%Y-%m')
        response = self.client.get(reverse('view_payslip', args=[self.staff.id, month_str]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, str(self.payslip.net_pay))

    def test_view_payslip_unauthenticated(self):
        """Test viewing payslip requires authentication"""
        month_str = self.payslip.month.strftime('%Y-%m')
        response = self.client.get(reverse('view_payslip', args=[self.staff.id, month_str]))
        self.assertEqual(response.status_code, 302)  # Redirect to login


class AdminViewsTestCase(BaseTestCase):
    """Test admin-specific views"""

    def setUp(self):
        super().setUp()
        self.client.login(email='<EMAIL>', password='testpass123')

    def test_admin_home(self):
        """Test admin dashboard"""
        response = self.client.get(reverse('admin_home'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Dashboard')
        self.assertIn('total_staff', response.context)
        self.assertIn('total_accountant', response.context)

    def test_admin_view_profile(self):
        """Test admin profile view"""
        response = self.client.get(reverse('admin_view_profile'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<EMAIL>')

    def test_division_list(self):
        """Test division listing"""
        response = self.client.get(reverse('division'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Division')

    def test_manage_division_get(self):
        """Test division creation form"""
        response = self.client.get(reverse('manage_division'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Add Division')

    def test_manage_division_post(self):
        """Test division creation"""
        response = self.client.post(reverse('manage_division'), {
            'name': 'New Division'
        })
        self.assertEqual(response.status_code, 302)
        self.assertTrue(Division.objects.filter(name='New Division').exists())

    def test_edit_division(self):
        """Test division editing"""
        response = self.client.get(reverse('manage_division', args=[self.division.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Edit Division')
        self.assertContains(response, 'Test Division')

    def test_delete_division(self):
        """Test division deletion"""
        division = Division.objects.create(name='To Delete')
        response = self.client.post(reverse('delete_division', args=[division.id]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(Division.objects.filter(id=division.id).exists())

    def test_department_list(self):
        """Test department listing"""
        response = self.client.get(reverse('department'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Department')

    def test_manage_department_get(self):
        """Test department creation form"""
        response = self.client.get(reverse('manage_department'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Add Department')

    def test_manage_department_post(self):
        """Test department creation"""
        response = self.client.post(reverse('manage_department'), {
            'code': 'ND01',
            'name': 'New Department',
            'division': self.division.id
        })
        self.assertEqual(response.status_code, 302)
        self.assertTrue(Department.objects.filter(code='ND01').exists())

    def test_designation_list(self):
        """Test designation listing"""
        response = self.client.get(reverse('designation'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Designation')

    def test_manage_designation_get(self):
        """Test designation creation form"""
        response = self.client.get(reverse('manage_designation'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Add Designation')

    def test_grade_list(self):
        """Test grade listing"""
        response = self.client.get(reverse('grade'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Grade')

    def test_manage_grade_get(self):
        """Test grade creation form"""
        response = self.client.get(reverse('manage_grade'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Add Grade')

    def test_fixed_list(self):
        """Test fixed values listing"""
        response = self.client.get(reverse('fixed'))
        self.assertEqual(response.status_code, 200)

    def test_manage_fixed_get(self):
        """Test fixed values creation form"""
        response = self.client.get(reverse('manage_fixed'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Add Fixed')

    def test_manage_accountant_list(self):
        """Test accountant listing"""
        response = self.client.get(reverse('manage_accountant'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<EMAIL>')

    def test_add_accountant_get(self):
        """Test accountant creation form"""
        response = self.client.get(reverse('add_accountant'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Add Accountant')

    def test_manage_staff_list(self):
        """Test staff listing"""
        response = self.client.get(reverse('manage_staff'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<EMAIL>')

    def test_add_staff_get(self):
        """Test staff creation form"""
        response = self.client.get(reverse('add_staff'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Add Staff')

    def test_get_dep_by_div_ajax(self):
        """Test AJAX endpoint for departments by division"""
        response = self.client.get(reverse('get_dep_by_div'), {
            'division_id': self.division.id
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('departments', data)

    def test_get_des_by_dep_ajax(self):
        """Test AJAX endpoint for designations by department"""
        response = self.client.get(reverse('get_des_by_dep'), {
            'department_id': self.department.id
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('designations', data)

    def test_check_email_availability_ajax(self):
        """Test AJAX endpoint for email availability"""
        response = self.client.get(reverse('check_email_availability'), {
            'email': '<EMAIL>'
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('is_available', data)


class AccountantViewsTestCase(BaseTestCase):
    """Test accountant-specific views"""

    def setUp(self):
        super().setUp()
        self.client.login(email='<EMAIL>', password='testpass123')

        # Create additional test data for payroll calculations
        self.deductions = Deductions.objects.create(
            staff=self.staff,
            income_tax=1000,
            canteen=500,
            advance=0,
            society=200,
            insurance=300,
            other=0
        )

        self.regular_pay = RegularPay.objects.create(
            staff=self.staff,
            arrears=2000,
            other=500
        )

        self.attendance = Attendance.objects.create(
            staff=self.staff,
            paid_days=30,
            lop=0
        )

    def test_accountant_home(self):
        """Test accountant dashboard"""
        response = self.client.get(reverse('accountant_home'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Dashboard')
        self.assertIn('total_staff', response.context)
        self.assertIn('active_staff', response.context)

    def test_accountant_view_profile(self):
        """Test accountant profile view"""
        response = self.client.get(reverse('accountant_view_profile'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<EMAIL>')

    def test_generate_payslip_regular_get(self):
        """Test regular payslip generation form"""
        response = self.client.get(reverse('generate_payslip_regular'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Generate Payslip')

    def test_generate_payslip_contract_get(self):
        """Test contract payslip generation form"""
        response = self.client.get(reverse('generate_payslip_contract'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Generate Payslip')

    def test_generate_payslip_active_get(self):
        """Test active payslip generation form"""
        response = self.client.get(reverse('generate_payslip_active'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Generate Payslip')

    def test_calculate_payroll_regular_post(self):
        """Test regular payroll calculation"""
        response = self.client.post(reverse('calculate_payroll_regular'), {
            'staff_id': self.staff.id,
            'month': date.today().strftime('%Y-%m'),
            'medical_allowance': 'no'
        })
        self.assertEqual(response.status_code, 200)
        # Should return JSON response with calculated values
        data = json.loads(response.content)
        self.assertIn('basic', data)
        self.assertIn('gross_pay', data)
        self.assertIn('net_pay', data)

    def test_staff_list(self):
        """Test staff listing for accountant"""
        response = self.client.get(reverse('staff_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<EMAIL>')

    def test_payroll_summary_report(self):
        """Test payroll summary report"""
        response = self.client.get(reverse('payroll_summary_report'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Payroll Summary')

    def test_download_payroll_summary(self):
        """Test payroll summary download"""
        current_month = date.today().strftime('%B')
        current_year = date.today().year
        response = self.client.get(reverse('download_payroll_summary',
                                         args=[current_month, current_year]))
        # Should return PDF response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')


class StaffViewsTestCase(BaseTestCase):
    """Test staff-specific views"""

    def setUp(self):
        super().setUp()
        self.client.login(email='<EMAIL>', password='testpass123')

    def test_staff_home(self):
        """Test staff dashboard"""
        response = self.client.get(reverse('staff_home'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Dashboard')
        self.assertIn('recent_payslips', response.context)

    def test_staff_view_profile(self):
        """Test staff profile view"""
        response = self.client.get(reverse('staff_view_profile'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<EMAIL>')
        self.assertContains(response, 'EMP001')  # Employee code

    def test_staff_list_payslips(self):
        """Test staff payslip listing"""
        response = self.client.get(reverse('staff_list_payslips'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Payslips')
        self.assertIn('payslips', response.context)


class PermissionTestCase(BaseTestCase):
    """Test access permissions for different user types"""

    def test_admin_cannot_access_accountant_views(self):
        """Test admin users cannot access accountant-specific views"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('accountant_home'))
        # Should redirect or return 403
        self.assertIn(response.status_code, [302, 403])

    def test_admin_cannot_access_staff_views(self):
        """Test admin users cannot access staff-specific views"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('staff_home'))
        # Should redirect or return 403
        self.assertIn(response.status_code, [302, 403])

    def test_accountant_cannot_access_admin_views(self):
        """Test accountant users cannot access admin-specific views"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('admin_home'))
        # Should redirect or return 403
        self.assertIn(response.status_code, [302, 403])

    def test_accountant_cannot_access_staff_views(self):
        """Test accountant users cannot access staff-specific views"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('staff_home'))
        # Should redirect or return 403
        self.assertIn(response.status_code, [302, 403])

    def test_staff_cannot_access_admin_views(self):
        """Test staff users cannot access admin-specific views"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('admin_home'))
        # Should redirect or return 403
        self.assertIn(response.status_code, [302, 403])

    def test_staff_cannot_access_accountant_views(self):
        """Test staff users cannot access accountant-specific views"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('accountant_home'))
        # Should redirect or return 403
        self.assertIn(response.status_code, [302, 403])


class TemplateTestCase(BaseTestCase):
    """Test template rendering and context"""

    def test_login_template_context(self):
        """Test login template receives correct context"""
        response = self.client.get(reverse('login_page'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'main_app/login.html')
        self.assertIn('form', response.context)
        self.assertIn('GOOGLE_RECAPTCHA_SITE_KEY', response.context)

    def test_admin_home_template_context(self):
        """Test admin home template receives correct context"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('admin_home'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'admin_template/home_content.html')

        # Check required context variables
        required_context = [
            'page_title', 'total_accountant', 'total_staff',
            'active_staff', 'inactive_staff', 'department_labels',
            'department_counts', 'recent_activities'
        ]
        for key in required_context:
            self.assertIn(key, response.context)

    def test_accountant_home_template_context(self):
        """Test accountant home template receives correct context"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('accountant_home'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accountant_template/home_content.html')

        # Check required context variables
        required_context = [
            'page_title', 'total_staff', 'active_staff',
            'regular_staff', 'contrect_staff'
        ]
        for key in required_context:
            self.assertIn(key, response.context)

    def test_staff_home_template_context(self):
        """Test staff home template receives correct context"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('staff_home'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'staff_template/home_content.html')

        # Check required context variables
        required_context = [
            'page_title', 'recent_payslips', 'start_date', 'end_date'
        ]
        for key in required_context:
            self.assertIn(key, response.context)

    def test_payslip_view_template_context(self):
        """Test payslip view template receives correct context"""
        self.client.login(email='<EMAIL>', password='testpass123')
        month_str = self.payslip.month.strftime('%Y-%m')
        response = self.client.get(reverse('view_payslip', args=[self.staff.id, month_str]))
        self.assertEqual(response.status_code, 200)

        # Check that payslip data is in context
        self.assertIn('payslip', response.context)
        self.assertIn('staff', response.context)

    def test_division_list_template_context(self):
        """Test division list template receives correct context"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('division'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'admin_template/manage_division.html')
        self.assertIn('divisions', response.context)

    def test_department_list_template_context(self):
        """Test department list template receives correct context"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('department'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'admin_template/manage_department.html')
        self.assertIn('departments', response.context)


class ModelTestCase(BaseTestCase):
    """Test model functionality and validation"""

    def test_custom_user_creation(self):
        """Test custom user model creation"""
        user = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            father_name='Test Father',
            user_type=3
        )
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.user_type, 3)
        self.assertTrue(user.check_password('testpass123'))

    def test_division_str_method(self):
        """Test Division model string representation"""
        self.assertEqual(str(self.division), 'Test Division')

    def test_department_str_method(self):
        """Test Department model string representation"""
        self.assertEqual(str(self.department), 'TD01 - Test Department')

    def test_designation_str_method(self):
        """Test Designation model string representation"""
        self.assertEqual(str(self.designation), 'Test Designation')

    def test_grade_str_method(self):
        """Test Grade model string representation"""
        self.assertEqual(str(self.grade), 'Test Grade')

    def test_staff_str_method(self):
        """Test Staff model string representation"""
        self.assertEqual(str(self.staff), 'Staff: <EMAIL>')

    def test_payslip_str_method(self):
        """Test Payslip model string representation"""
        expected = f"Payslip for {self.staff} - {self.payslip.month:%B %Y}"
        self.assertEqual(str(self.payslip), expected)

    def test_grade_validation(self):
        """Test Grade model validation"""
        # Test invalid grade (start > end)
        with self.assertRaises(ValidationError):
            grade = Grade(
                name='Invalid Grade',
                start=50000,
                end=10000,  # End less than start
                increment=1000,
                medical=500,
                adhoc=1000,
                conva=500,
                cca=200
            )
            grade.full_clean()

    def test_payslip_unique_constraint(self):
        """Test Payslip unique constraint (staff, month)"""
        # Try to create duplicate payslip for same staff and month
        with self.assertRaises(Exception):
            Payslip.objects.create(
                staff=self.staff,
                month=self.payslip.month,  # Same month as existing payslip
                basic=20000,
                gross_pay=30000,
                net_pay=25000
            )

    def test_user_profile_creation_signal(self):
        """Test that user profiles are created automatically"""
        # Create admin user
        admin_user = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='New',
            last_name='Admin',
            father_name='Admin Father',
            user_type=1
        )
        # Check that Admin profile was created
        self.assertTrue(Admin.objects.filter(user=admin_user).exists())

        # Create accountant user
        accountant_user = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='New',
            last_name='Accountant',
            father_name='Accountant Father',
            user_type=2
        )
        # Check that Accountant profile was created
        self.assertTrue(Accountant.objects.filter(user=accountant_user).exists())

        # Create staff user
        staff_user = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='New',
            last_name='Staff',
            father_name='Staff Father',
            user_type=3
        )
        # Check that Staff profile was created
        self.assertTrue(Staff.objects.filter(user=staff_user).exists())


class ErrorHandlingTestCase(BaseTestCase):
    """Test error handling and edge cases"""

    def test_404_error_handling(self):
        """Test 404 error for non-existent resources"""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Test non-existent division
        response = self.client.get(reverse('manage_division', args=[99999]))
        self.assertEqual(response.status_code, 404)

        # Test non-existent staff
        response = self.client.get(reverse('edit_staff', args=[99999]))
        self.assertEqual(response.status_code, 404)

    def test_invalid_payslip_view(self):
        """Test viewing non-existent payslip"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('view_payslip', args=[99999, '2023-01']))
        self.assertEqual(response.status_code, 404)

    def test_invalid_ajax_requests(self):
        """Test invalid AJAX requests"""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Test with invalid division ID
        response = self.client.get(reverse('get_dep_by_div'), {
            'division_id': 99999
        }, HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertEqual(len(data['departments']), 0)

    def test_form_validation_errors(self):
        """Test form validation error handling"""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Test division creation with empty name
        response = self.client.post(reverse('manage_division'), {
            'name': ''  # Empty name should cause validation error
        })
        # Should stay on same page with error
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'error')  # Should show error message
