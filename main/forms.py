import logging
from django import forms
from django.forms import Model<PERSON><PERSON>iceField
from django.forms.widgets import DateInput
from .models import *

# Initialize logger
logger = logging.getLogger(__name__)

GENDER_CHOICES = [
    ('M', 'Male'),
    ('F', 'Female'),
    ('O', 'Other')
]

EMPLOYMENT_TYPE = [
    ('Regular', 'Regular'),
    ('Contract', 'Contract'),
    ('Active', 'Active')
]

class LoginForm(forms.Form):
    """
    Form for user login with email and password fields.
    """
    email = forms.EmailField(
        label="Email",
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Enter email'}),
    )
    password = forms.CharField(
        label="Password",
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'Enter password'}),
    )
    remember_me = forms.BooleanField(required=False, label='Remember Me')

class FormSettings(forms.ModelForm):
    """
    Base form class to apply common styling and settings to form fields.
    """
    def __init__(self, *args, **kwargs):
        super(FormSettings, self).__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            widget = field.widget
            if isinstance(widget, forms.Select):
                widget.attrs.update({
                    'class': 'form-control select2bs4',
                })
            elif isinstance(widget, forms.ClearableFileInput):
                field.widget.attrs['class'] = 'form-control-file'
            elif isinstance(widget, forms.CheckboxInput):
                widget.attrs.update({
                    'class': 'form-check-input',
                })
            else:
                widget.attrs.update({
                    'class': 'form-control',
                    'placeholder': field.label,
                })

class CustomUserForm(FormSettings):
    """
    Form for creating and updating CustomUser instances.
    """
    email = forms.EmailField(required=True)
    gender = forms.ChoiceField(choices=GENDER_CHOICES, widget=forms.Select(attrs={'class': 'form-control'}))
    first_name = forms.CharField(required=True)
    last_name = forms.CharField(required=True)
    password = forms.CharField(widget=forms.PasswordInput, required=False)
    profile_pic = forms.ImageField(required=False)
    father_name = forms.CharField(required=True)

    class Meta:
        model = CustomUser
        fields = ['first_name', 'last_name', 'email', 'gender', 'father_name', 'password', 'profile_pic']
        widgets = {'password': forms.PasswordInput(attrs={'class': 'form-control'})}
        labels = {
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'email': 'Email Address',
            'gender': 'Gender',
            'father_name': 'Father/Husband Name',
            'password': 'Password',
            'profile_pic': 'Profile Picture',
        }

    def clean_email(self):
        """
        Validate that the email is unique unless updating the current user's email.
        """
        formEmail = self.cleaned_data['email'].lower()
        if self.instance.pk is None:  # Insert
            if CustomUser.objects.filter(email=formEmail).exists():
                raise forms.ValidationError("The given email is already registered.")
        else:  # Update
            # Get the current email from the instance
            if hasattr(self.instance, 'user'):
                # For Admin, Accountant, Staff forms
                dbEmail = self.instance.user.email.lower()
            else:
                # For CustomUser form directly
                dbEmail = CustomUser.objects.get(id=self.instance.pk).email.lower()

            if dbEmail != formEmail:  # Email has changed
                if CustomUser.objects.filter(email=formEmail).exists():
                    raise forms.ValidationError("The given email is already registered.")

        return formEmail

    def __init__(self, *args, **kwargs):
        """
        Initialize the form and set initial values from the user instance if provided.
        """
        super(CustomUserForm, self).__init__(*args, **kwargs)
        if kwargs.get('instance'):
            user_instance = kwargs['instance']
            if isinstance(user_instance, CustomUser):
                for field_name in self.fields:
                    if hasattr(user_instance, field_name):
                        self.fields[field_name].initial = getattr(user_instance, field_name)
            else:
                user_instance = kwargs['instance'].user
                for field_name in self.fields:
                    if hasattr(user_instance, field_name):
                        self.fields[field_name].initial = getattr(user_instance, field_name)
            if self.instance.pk is not None:
                self.fields['password'].widget.attrs['placeholder'] = "Fill this only if you wish to update the password"

class AdminForm(CustomUserForm):
    """
    Form for creating and updating Admin instances.
    """
    class Meta(CustomUserForm.Meta):
        model = Admin
        fields = CustomUserForm.Meta.fields

class AccountantForm(CustomUserForm):
    """
    Form for creating and updating Accountant instances.
    """
    class Meta(CustomUserForm.Meta):
        model = Accountant
        fields = CustomUserForm.Meta.fields

    def __init__(self, *args, **kwargs):
        super(AccountantForm, self).__init__(*args, **kwargs)

class StaffForm(CustomUserForm):
    """
    Form for creating and updating Staff instances with additional fields.
    """
    class Meta(CustomUserForm.Meta):
        model = Staff
        fields = CustomUserForm.Meta.fields + [
            'division', 'designation', 'department', 'emp_code', 'uan', 'emp_doj', 'grade', 'basic_amt', 'employment_type', 'is_active','cca'
        ]

        widgets = {
            'emp_doj': forms.DateInput(attrs={'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        super(StaffForm, self).__init__(*args, **kwargs)

class DivisionForm(FormSettings):
    """
    Form for creating and updating Division instances.
    """
    class Meta:
        model = Division
        fields = ['name']
        labels = {
            'name': 'Division Name',
        }

    def clean_name(self):
        """
        Validate that the division name is unique.
        """
        name = self.cleaned_data['name']
        if Division.objects.filter(name__iexact=name).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError("Division name already exists.")
        return name

class DepartmentForm(FormSettings):
    """
    Form for creating and updating Department instances.
    """
    class Meta:
        fields = ['division', 'name', 'code']
        model = Department
        labels = {
            'division': 'Division',
            'name': 'Department Name',
            'code': 'Department Code',
        }

    def clean_code(self):
        """
        Validate that the department code is unique and in uppercase.
        """
        code = self.cleaned_data['code'].upper()
        if Department.objects.filter(code__iexact=code).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError("Department code already exists.")
        return code

    def clean_name(self):
        """
        Validate that the department name is unique and stripped of whitespace.
        """
        name = self.cleaned_data['name'].strip()
        if Department.objects.filter(name__iexact=name).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError("Department name already exists.")
        return name

class DesignationForm(FormSettings):
    """
    Form for creating and updating Designation instances.
    """
    class Meta:
        model = Designation
        fields = ['department', 'name']
        labels = {
            'department': 'Department',
            'name': 'Designation Name',
        }

    def clean_name(self):
        """
        Validate that the designation name is unique within the selected department.
        """
        name = self.cleaned_data['name'].strip()
        department = self.cleaned_data['department']
        if Designation.objects.filter(department=department, name__iexact=name).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError("Designation with this name already exists in this department.")
        return name

class GradeForm(FormSettings):
    """
    Form for creating and updating Grade instances with salary and allowance details.
    """
    class Meta:
        model = Grade
        fields = ['name', 'start', 'end', 'increment', 'adhoc', 'conva']
        labels = {
            'name': 'Grade Name',
            'start': 'Starting Salary (₹)',
            'end': 'Ending Salary (₹)',
            'increment': 'Annual Increment (₹)',
            'adhoc': 'Adhoc Relief Allowance (₹)',
            'conva': 'Conveyance Allowance (₹)',
        }

    def clean(self):
        """
        Validate that salary ranges and increments are correct.
        """
        cleaned_data = super().clean()

        start = cleaned_data.get('start')
        end = cleaned_data.get('end')
        increment = cleaned_data.get('increment')

        if start and end and start > end:
            raise forms.ValidationError("Starting salary cannot be greater than ending salary.")

        if increment and increment <= 0:
            raise forms.ValidationError("Increment must be greater than zero.")

        return cleaned_data

class DeductionForm(FormSettings):
    """
    Form for creating and updating Deductions instances.
    """
    staff = ModelChoiceField(
        queryset=Staff.objects.filter(is_active=True),
        required=True,
    )

    class Meta:
        fields = ['staff', 'society', 'income_tax', 'canteen', 'advance', 'insurance', 'other']
        model = Deductions

class FixedForm(FormSettings):
    """
    Form for creating and updating Fixed instances with monthly allowances.
    """
    def __init__(self, *args, **kwargs):
        super(FixedForm, self).__init__(*args, **kwargs)
        self.fields['month'].widget = DateInput(attrs={'type': 'date'})

    class Meta:
        model = Fixed
        fields = ['division','month', 'da', 'hra']
        labels = {
            'month': 'Month and Year',
            'da': 'Dearness Allowance (%)',
            'hra': 'House Rent Allowance (%)',
            'division': 'Division',
        }

class ContractPayForm(FormSettings):
    """
    Form for creating and updating ContractPay instances.
    """
    class Meta:
        model = ContractPay
        fields = ['staff', 'month', 'adhoc', 'hra', 'arrears', 'other']

class RegularPayForm(FormSettings):
    """
    Form for creating and updating RegularPay instances.
    """
    class Meta:
        model = RegularPay
        fields = ['staff', 'arrears', 'other']

class AttendanceForm(FormSettings):
    """
    Form for creating and updating Attendance instances.
    """
    class Meta:
        model = Attendance
        fields = ['staff', 'paid_days', 'lop']
