from django.urls import path, include
from main import views, admin_views, accountant_views, staff_views

# Common URL patterns accessible to all users
common_urlpatterns = [
    path("", views.login_page, name="login_page"),
    path("doLogin/", views.doLogin, name="user_login"),
    path("logout_user/", views.logout_user, name="user_logout"),

    path("list/payslip/<str:employment_type>", views.list_payslip, name='list_payslip_type'),
    path("list/payslip/", views.list_payslip, name='list_payslip'),

    path('view/payslip/<int:staff_id>/<str:month>/', views.view_payslip, name='view_payslip'),

    path('send_email/<int:staff_id>/<str:month>/', views.send_payroll_email, name='send_payroll_email'),
    path('send_bulk_email/', views.send_bulk_email, name='send_bulk_email'),

    path('save-fixed/', views.save_fixed, name='save_fixed'),

    path('save-deduction-details/', views.save_deduction_details, name='save_deduction_details'),
    path('save-attendence-details/', views.save_attendence_details, name='save_attendence_details'),

    path('save_contract_pay/', views.save_contract_pay, name='save_contract_pay'),
    path('save_regular_pay/', views.save_regular_pay, name='save_regular_pay'),

]

# URL patterns for Admin users
admin_urlpatterns = [
    path("home/", admin_views.admin_home, name="admin_home"),  # Disabled: Using Django admin instead
    path("admin_view_profile/", admin_views.admin_view_profile, name="admin_view_profile"),

    path("division/add", admin_views.manage_division, name='manage_division'),
    path("division/", admin_views.division, name='division'),
    path("division/delete/<int:division_id>", admin_views.delete_division, name='delete_division'),
    path("division/edit/<int:division_id>", admin_views.manage_division, name='manage_division'),

    path("department/", admin_views.department, name='department'),
    path("department/add", admin_views.manage_department, name='manage_department'),
    path("department/delete/<int:department_id>", admin_views.delete_department, name='delete_department'),
    path("department/edit/<int:department_id>", admin_views.manage_department, name='manage_department'),

    path("designation/", admin_views.designation, name='designation'),
    path("designation/delete/<int:designation_id>", admin_views.delete_designation, name='delete_designation'),
    path("designation/edit/<int:designation_id>", admin_views.manage_designation, name='manage_designation'),
    path("designation/add", admin_views.manage_designation, name='manage_designation'),

    path("grade/add/", admin_views.manage_grade, name='manage_grade'),
    path("grade/", admin_views.grade, name='grade'),
    path("grade/delete/<int:grade_id>", admin_views.delete_grade, name='delete_grade'),
    path("grade/edit/<int:grade_id>", admin_views.manage_grade, name='manage_grade'),

    path("fixed/add/", admin_views.manage_fixed, name='manage_fixed'),
    path("fixed/", admin_views.fixed, name='fixed'),
    path("fixed/delete/<int:fixed_id>", admin_views.delete_fixed, name='delete_fixed'),
    path("fixed/edit/<int:fixed_id>", admin_views.manage_fixed, name='manage_fixed'),

    path("accountant/add", admin_views.add_accountant, name='add_accountant'),
    path("accountant/", admin_views.manage_accountant, name='manage_accountant'),
    path("accountant/edit/<int:accountant_id>", admin_views.edit_accountant, name='edit_accountant'),
    path("accountant/delete/<int:accountant_id>", admin_views.delete_accountant, name='delete_accountant'),

    path("staff/add/", admin_views.add_staff, name='add_staff'),
    path("staff/", admin_views.manage_staff, name='manage_staff'),
    path("staff/delete/<int:staff_id>", admin_views.delete_staff, name='delete_staff'),
    path("staff/edit/<int:staff_id>", admin_views.edit_staff, name='edit_staff'),

    path('get_dep_by_div/', admin_views.get_dep_by_div, name='get_dep_by_div'),
    path('get_des_by_dep/', admin_views.get_des_by_dep, name='get_des_by_dep'),
    path("check_email_availability", admin_views.check_email_availability,name="check_email_availability"),

    path('edit/payslip/', admin_views.edit_payslip, name='edit_payslip'),

    path('edit/payroll/regular', admin_views.edit_payroll_regular, name='edit_payroll_regular'),
    path('edit/payroll/contract', admin_views.edit_payroll_contract, name='edit_payroll_contract'),
    path('edit/payroll/active', admin_views.edit_payroll_active, name='edit_payroll_active'),
]

# URL patterns for Staff users
staff_urlpatterns = [
    path("home/", staff_views.staff_home, name="staff_home"),
    path("view/profile/", staff_views.staff_view_profile, name="staff_view_profile"),

    path("list/payslip/", staff_views.staff_list_payslips, name="staff_list_payslips"),
]

# URL patterns for Accountant users
accountant_urlpatterns = [
    path("home/", accountant_views.accountant_home, name="accountant_home"),
    path("view/profile/", accountant_views.accountant_view_profile, name="accountant_view_profile"),

    path("generate/payslip/regular", accountant_views.generate_payslip_regular, name='generate_payslip_regular'),
    path("generate/payslip/contract", accountant_views.generate_payslip_contract, name='generate_payslip_contract'),
    path("generate/payslip/active", accountant_views.generate_payslip_active, name='generate_payslip_active'),

    path('calculate/payroll/regular', accountant_views.calculate_payroll_regular, name='calculate_payroll_regular'),
    path('calculate/payroll/contract', accountant_views.calculate_payroll_contract, name='calculate_payroll_contract'),
    path('calculate/payroll/active', accountant_views.calculate_payroll_active, name='calculate_payroll_active'),

    path("staff_list/", accountant_views.staff_list, name="staff_list"),

    path("payroll_summary_report/", accountant_views.payroll_summary_report, name='payroll_summary_report'),
    path('download_payroll_summary/<str:month>/<int:year>/', accountant_views.download_payroll_summary, name='download_payroll_summary'),
]

# Main URL patterns combining all others
urlpatterns = common_urlpatterns + [
    path('administrator/', include(admin_urlpatterns)),
    path('staff/', include(staff_urlpatterns)),
    path('accountant/', include(accountant_urlpatterns)),
]
