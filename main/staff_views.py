from django.shortcuts import get_object_or_404, render
from datetime import datetime, timedelta
from .forms import *
from .models import *

def staff_home(request):
    # Get the staff object for the logged-in user
    staff = get_object_or_404(Staff, user=request.user)

    # Calculate date range for recent payslips (last 40 days)
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=40)

    # Retrieve recent payslips within the specified date range
    recent_payslips = Payslip.objects.filter(
        staff=staff,
        month__gte=start_date,
        month__lte=end_date
    ).order_by('-month')

    # Context data to be passed to the template
    context = {
        'page_title': 'Staff Dashboard',
        'recent_payslips': recent_payslips,
        'start_date': start_date.strftime('%B %Y'),
        'end_date': end_date.strftime('%B %Y'),
    }

    return render(request, 'staff_template/home_content.html', context)

def staff_view_profile(request):
    # Get the staff object for the logged-in user
    staff = get_object_or_404(Staff, user=request.user)

    # Context data to be passed to the template
    context = {
        'page_title': 'Profile',
        'staff': staff,
    }

    return render(request, "staff_template/staff_view_profile.html", context)

def staff_list_payslips(request):
    # Get the staff object for the logged-in user
    staff = get_object_or_404(Staff, user=request.user)

    # Calculate date range for listing payslips (last 180 days)
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=180)

    # Retrieve payslips within the specified date range
    recent_payslips = Payslip.objects.filter(
        staff=staff,
        month__gte=start_date,
        month__lte=end_date
    ).order_by('-month')

    # Context data to be passed to the template
    context = {
        'page_title': 'List of Payslips',
        'payslips': recent_payslips,
    }

    return render(request, "staff_template/staff_list_payslips.html", context)
