# Python
*.pyc
*.pyo
*.pyd
__pycache__/
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/
# Note: static/ is now tracked for AdminLTE assets

# Django Migrations
*/migrations/*
!*/migrations/__init__.py

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env.staging

# IDE and Editors
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Backup files
*.bak
*.backup
*.tmp

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Node.js (if using frontend tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Celery
celerybeat-schedule
celerybeat.pid

# Redis
dump.rdb

# Documentation
docs/
